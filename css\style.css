/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Simplified Color Palette */
    --primary-color: #4a9eff;
    --secondary-color: #6c757d;
    --accent-color: #28a745;
    --bg-dark: #1a1a1a;
    --bg-darker: #0f0f0f;
    --bg-card: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #adb5bd;
    --text-accent: #4a9eff;
    --border-color: #404040;
    --gradient-primary: linear-gradient(135deg, #4a9eff, #28a745);
    --gradient-secondary: linear-gradient(135deg, #6c757d, #495057);

    /* Typography */
    --font-primary: 'Roboto', sans-serif;
    --font-secondary: 'Roboto', sans-serif;

    /* Spacing */
    --section-padding: 80px 0;
    --container-padding: 0 20px;

    /* Transitions */
    --transition-fast: 0.3s ease;
    --transition-medium: 0.5s ease;
    --transition-slow: 0.8s ease;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    background-color: var(--bg-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-fast);
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.98);
    box-shadow: 0 2px 20px rgba(0, 255, 65, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.logo-subtitle {
    font-size: 0.7rem;
    color: var(--text-secondary);
    margin-top: -5px;
    letter-spacing: 2px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.name-highlight {
    color: var(--primary-color);
}

.surname {
    color: var(--text-primary);
    display: block;
    margin-top: 0.5rem;
}

.typing-container {
    margin-bottom: 1.5rem;
    height: 2.5rem;
}

.typing-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.cursor {
    color: var(--primary-color);
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: var(--transition-fast);
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--bg-dark);
}

.btn-primary:hover {
    opacity: 0.9;
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--bg-dark);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-container {
    position: relative;
}

.cyber-frame {
    position: relative;
    width: 250px;
    height: 250px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    padding: 10px;
    background: rgba(74, 158, 255, 0.1);
}

.profile-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--bg-card);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
    padding: 1rem;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-arrow {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Section Styles */
section {
    padding: var(--section-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-line {
    width: 80px;
    height: 3px;
    background: var(--gradient-primary);
    margin: 0 auto;
    border-radius: 2px;
}

/* About Section */
.about {
    background: var(--bg-darker);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-highlights {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.highlight-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.stat-item:hover {
    border-color: var(--primary-color);
}

.stat-number {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Education Section */
.education {
    background: var(--bg-dark);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    width: 50%;
}

.timeline-item:nth-child(odd) {
    left: 0;
    padding-right: 2rem;
}

.timeline-item:nth-child(even) {
    left: 50%;
    padding-left: 2rem;
}

.timeline-dot {
    position: absolute;
    top: 0;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border: 3px solid var(--bg-dark);
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
}

.timeline-item:nth-child(odd) .timeline-dot {
    right: -11px;
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -11px;
}

.timeline-content {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    position: relative;
}

.timeline-content:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.1);
}

.timeline-date {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.timeline-title {
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.timeline-subtitle {
    color: var(--secondary-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-institution {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

.timeline-achievement {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 255, 65, 0.1);
    border-radius: 20px;
    width: fit-content;
}

.timeline-achievement i {
    color: var(--primary-color);
}

.timeline-achievement span {
    color: var(--text-primary);
    font-weight: 600;
}

.timeline-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Skills Section */
.skills {
    background: var(--bg-darker);
}

.skills-categories {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.skill-category {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.skill-category:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.1);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.category-header i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.category-header h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0;
    transition: width 2s ease;
    border-radius: 4px;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.skill-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(0, 255, 65, 0.1);
    border-radius: 8px;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-fast);
}

.skill-badge:hover {
    background: rgba(0, 255, 65, 0.2);
    transform: scale(1.05);
}

.skill-badge i {
    color: var(--primary-color);
}

.soft-skills {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.soft-skill-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(0, 102, 204, 0.1);
    border-radius: 8px;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-fast);
}

.soft-skill-item:hover {
    background: rgba(0, 102, 204, 0.2);
    transform: translateX(5px);
}

.soft-skill-item i {
    color: var(--secondary-color);
    font-size: 1.2rem;
}

/* Projects Section */
.projects {
    background: var(--bg-dark);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--bg-card);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-fast);
}

.project-card:hover {
    border-color: var(--primary-color);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.project-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-dark);
    font-size: 1.5rem;
}

.project-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.project-title {
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-features {
    margin-bottom: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.feature-item i {
    color: var(--primary-color);
    font-size: 0.8rem;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tech-tag {
    background: rgba(0, 255, 65, 0.1);
    color: var(--primary-color);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-achievement,
.project-publication {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 8px;
    color: var(--accent-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.project-publication {
    background: rgba(0, 102, 204, 0.1);
    color: var(--secondary-color);
}

/* Experience Section */
.experience {
    background: var(--bg-darker);
}

.experience-content {
    max-width: 800px;
    margin: 0 auto;
}

.experience-card {
    background: var(--bg-card);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
    transition: var(--transition-fast);
}

.experience-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 15px 35px rgba(0, 255, 65, 0.1);
}

.experience-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.company-logo {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-dark);
    font-size: 1.5rem;
}

.experience-details {
    flex: 1;
}

.position-title {
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
}

.company-name {
    color: var(--secondary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.experience-period {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.status-badge {
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.current {
    background: rgba(0, 255, 65, 0.2);
    color: var(--primary-color);
}

.status-badge.academic {
    background: rgba(0, 102, 204, 0.2);
    color: var(--secondary-color);
}

.experience-description p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.experience-highlights {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.experience-highlights .highlight-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.experience-highlights .highlight-item i {
    color: var(--primary-color);
}

.experience-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Achievements Section */
.achievements {
    background: var(--bg-dark);
}

.achievements-grid {
    display: grid;
    gap: 2rem;
}

.achievement-category {
    background: var(--bg-card);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.achievement-category:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 15px 35px rgba(0, 255, 65, 0.1);
}

.category-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.category-title i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.achievement-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.achievement-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(0, 255, 65, 0.05);
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
    transition: var(--transition-fast);
}

.achievement-item:hover {
    background: rgba(0, 255, 65, 0.1);
    transform: translateX(5px);
}

.achievement-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-dark);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.achievement-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.achievement-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.achievement-date {
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 600;
}

.certification-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 102, 204, 0.1);
    border-radius: 10px;
    transition: var(--transition-fast);
}

.cert-item:hover {
    background: rgba(0, 102, 204, 0.2);
    transform: scale(1.02);
}

.cert-logo {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.cert-details h4 {
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.cert-issuer {
    color: var(--secondary-color);
    font-size: 0.8rem;
    font-weight: 500;
    display: block;
}

.cert-date {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.publication-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 10px;
    border-left: 4px solid var(--accent-color);
}

.publication-icon {
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.publication-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.publication-venue {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.publication-date {
    color: var(--accent-color);
    font-size: 0.8rem;
    font-weight: 600;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 65, 0.05);
    border-radius: 8px;
    color: var(--text-secondary);
    line-height: 1.6;
}

.activity-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

/* Contact Section */
.contact {
    background: var(--bg-darker);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-subtitle {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.contact-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-details {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-dark);
    font-size: 1.2rem;
}

.contact-text h4 {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.contact-text a,
.contact-text span {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-text a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    font-size: 1.2rem;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--bg-dark);
    border-color: var(--primary-color);
    transform: translateY(-3px);
}

.contact-form-container {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-color);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
    background: var(--bg-darker);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    color: var(--text-primary);
    font-family: var(--font-secondary);
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 255, 65, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--bg-darker);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-secondary);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-darker);
        width: 100%;
        text-align: center;
        transition: var(--transition-fast);
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        width: 100%;
        left: 0 !important;
        padding-left: 3rem !important;
        padding-right: 0 !important;
    }

    .timeline-dot {
        left: 11px !important;
        right: auto !important;
    }

    .skills-categories {
        grid-template-columns: 1fr;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .soft-skills {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .experience-highlights {
        grid-template-columns: 1fr;
    }

    .certification-grid {
        grid-template-columns: 1fr;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
